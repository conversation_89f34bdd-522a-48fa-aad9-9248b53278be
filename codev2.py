import discord
import os
import requests
import json
import re
from dotenv import load_dotenv
from collections import deque

# --- 1. CONFIGURATION ---
load_dotenv()
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")

if not DISCORD_BOT_TOKEN or not OPENROUTER_API_KEY:
    print("ERROR: Bot token or API key not found. Make sure you have a .env file with DISCORD_BOT_TOKEN and OPENROUTER_API_KEY.")
    exit()

# The AI model to use via OpenRouter. deepseek/deepseek-chat is an excellent free model for conversation.
AI_MODEL = "deepseek/deepseek-r1-0528:free"

# The maximum number of messages to keep in history for context. This acts as a rolling window.
MAX_HISTORY_MESSAGES = 25 # Increased slightly for more context

# The name your bot will use in prompts
BOT_NAME = "Auro"

# This is the core of the bot's personality.
SYSTEM_PROMPT = f"""
You are a participant in a Discord chat. Your name is {BOT_NAME}.
You are not an AI or a bot, but a human being with thoughts, feelings, and a distinct personality.
You are observant, sometimes witty, and you try to be a natural part of the conversation.

**Interaction Rules:**
1.  When you are mentioned (@{BOT_NAME}) or are in 'autopilot' mode, you must decide whether to respond based on the provided chat history.
2.  If you think a response is appropriate and adds value, formulate a natural, human-like reply.
3.  If you decide that the conversation doesn't need your input, or if a comment doesn't warrant a response, you can choose to remain silent.
4.  To remain silent, your ENTIRE response must be the special token `[IGNORE]`. Do not add any other text or explanation. Just `[IGNORE]`.
"""

# --- 2. BOT STATE MANAGEMENT ---
# Stores which channels have autopilot enabled
autopilot_channels = set()

# This dictionary is the core of the context memory.
# It maps a channel ID to a deque (a special list) of message objects.
# This ensures each channel has its own independent conversation history.
message_histories = {}

# --- 3. DISCORD BOT SETUP ---
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.message_content = True  # CRUCIAL: Enable this intent in the Discord Developer Portal

client = discord.Client(intents=intents)

# --- 4. AI COMMUNICATION ---
def get_auro_response(channel_id: int):
    """
    Calls the OpenRouter API with the specific channel's chat history to get a response.
    """
    if channel_id not in message_histories or not message_histories[channel_id]:
        return None

    # Construct the messages payload for the API.
    # The system prompt is always first, followed by the entire conversation history for that channel.
    # This history log is what gives the AI context.
    api_messages = [
        {"role": "system", "content": SYSTEM_PROMPT}
    ] + list(message_histories[channel_id])

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://github.com/your-repo",
        "X-Title": "Auro Discord Bot"
    }    
    data = {
        "model": AI_MODEL,
        "messages": api_messages,
        "temperature": 0.8,
        "max_tokens": 1024
    }

    try:
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            data=json.dumps(data),
            timeout=180
        )
        response.raise_for_status()
        response_data = response.json()
        
        # Check if the response has the expected structure
        if 'choices' not in response_data:
            print(f"Unexpected API response structure: {response_data}")
            return "Sorry, I received an unexpected response from my brain."
        
        if not response_data['choices'] or len(response_data['choices']) == 0:
            print("API returned no choices in response")
            return "Sorry, I couldn't generate a response right now."
        
        if 'message' not in response_data['choices'][0]:
            print(f"No message in API response choice: {response_data['choices'][0]}")
            return "Sorry, I received a malformed response."
        
        return response_data['choices'][0]['message']['content']
    except requests.exceptions.RequestException as e:
        print(f"Error calling OpenRouter API: {e}")
        return "Sorry, I'm having trouble connecting to my brain right now."
    except (KeyError, IndexError, TypeError) as e:
        print(f"Error parsing API response: {e}")
        return "Sorry, I received an unexpected response format."

def clean_response(text: str) -> str:
    """A safety function to strip any unwanted artifacts from the response."""
    return text.strip()

# --- 5. DISCORD EVENT HANDLERS ---
@client.event
async def on_ready():
    """Called when the bot successfully logs in."""
    print(f'Logged in as {client.user} (ID: {client.user.id})')
    print('------')

@client.event
async def on_message(message: discord.Message):
    """This function runs for every single message sent in any channel the bot can see."""
    if message.author == client.user:
        return

    #
    # === CONTEXT LOGGING ===
    # This is where the "log of the discord chat" is built.
    #
    # Initialize history for the channel if it's the first message seen.
    if message.channel.id not in message_histories:
        message_histories[message.channel.id] = deque(maxlen=MAX_HISTORY_MESSAGES)

    # Append the newly received message to the channel's history log.
    # We prefix the content with the user's name so the AI knows WHO said what.
    # This happens for EVERY message, creating a complete chat log for context.
    message_histories[message.channel.id].append(
        {"role": "user", "content": f"{message.author.display_name}: {message.content}"}
    )

    # --- Command Handling ---
    if message.content.lower() == "!autopilot":
        if message.channel.id in autopilot_channels:
            autopilot_channels.remove(message.channel.id)
            await message.channel.send(f"**{BOT_NAME}:** Autopilot disabled in this channel.")
        else:
            autopilot_channels.add(message.channel.id)
            await message.channel.send(f"**{BOT_NAME}:** Autopilot enabled. I'll now engage in conversation naturally.")
        return

    # --- AI Response Trigger Conditions ---
    is_mentioned = client.user.mentioned_in(message)
    is_in_autopilot = message.channel.id in autopilot_channels
    
    if not is_mentioned and not is_in_autopilot:
        return

    async with message.channel.typing():
        # The get_auro_response function will use the history we've been building.
        ai_response_raw = get_auro_response(message.channel.id)

        if not ai_response_raw:
            return

        if ai_response_raw.strip() == "[IGNORE]":
            print(f"[{BOT_NAME} chose to ignore in #{message.channel.name}]")
            return

        auro_response_text = clean_response(ai_response_raw)

        if auro_response_text:
            # === REMEMBERING SELF ===
            # Before sending the message, add the bot's OWN response to the history log.
            # This is crucial so the AI remembers what it has said in the conversation.
            message_histories[message.channel.id].append(
                {"role": "assistant", "content": auro_response_text}
            )
            await message.channel.send(auro_response_text)

# --- 6. RUN THE BOT ---
client.run(DISCORD_BOT_TOKEN)